# Process Management Test Script
param([switch]$Help)

$ErrorActionPreference = "Stop"

$SCRIPT_DIR = Split-Path -Parent $MyInvocation.MyCommand.Path
$START_SCRIPT = Join-Path $SCRIPT_DIR "start-server.ps1"
$LOG_DIR = Join-Path $SCRIPT_DIR "logs"
$PID_FILE = Join-Path $LOG_DIR "server.pid"

function Write-TestLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    $color = switch ($Level) {
        "INFO" { "Blue" }
        "SUCCESS" { "Green" }
        "WARNING" { "Yellow" }
        "ERROR" { "Red" }
        default { "White" }
    }
    Write-Host "[$Level] $timestamp - $Message" -ForegroundColor $color
}

function Show-Help {
    Write-Host "Process Management Test Script"
    Write-Host ""
    Write-Host "Usage: .\test-process-management.ps1 [-Help]"
    Write-Host ""
    Write-Host "This script tests:"
    Write-Host "  1. PID file generation and deletion"
    Write-Host "  2. status command functionality"
    Write-Host "  3. stop command functionality"
    Write-Host "  4. background process management"
    Write-Host ""
}

function Test-PidFileGeneration {
    Write-TestLog "Test 1: PID file generation" "INFO"

    # Stop any running server
    Write-TestLog "Stopping any running server..." "INFO"
    & $START_SCRIPT stop | Out-Null

    # Ensure PID file doesn't exist
    if (Test-Path $PID_FILE) {
        Remove-Item $PID_FILE -Force
        Write-TestLog "Cleaned up existing PID file" "INFO"
    }

    # Start server in background
    Write-TestLog "Starting server in background..." "INFO"
    & $START_SCRIPT start -Background | Out-Null

    # Check if PID file is generated
    Start-Sleep -Seconds 2
    if (Test-Path $PID_FILE) {
        $serverPid = Get-Content $PID_FILE
        Write-TestLog "PID file generated: $PID_FILE (PID: $serverPid)" "SUCCESS"

        # Verify process exists
        $process = Get-Process -Id $serverPid -ErrorAction SilentlyContinue
        if ($process) {
            Write-TestLog "Process exists (PID: $serverPid)" "SUCCESS"
            return $true
        } else {
            Write-TestLog "PID file exists but process doesn't" "ERROR"
            return $false
        }
    } else {
        Write-TestLog "PID file not generated" "ERROR"
        return $false
    }
}

function Test-StatusCommand {
    Write-TestLog "Test 2: status command" "INFO"

    # Execute status command
    Write-TestLog "Executing status command..." "INFO"
    $statusOutput = & $START_SCRIPT status 2>&1

    # Check if output contains expected information
    $statusText = $statusOutput -join "`n"
    if ($statusText -match "PID:.*\d+") {
        Write-TestLog "Status command shows server running" "SUCCESS"
        return $true
    } else {
        Write-TestLog "Status command output unexpected" "ERROR"
        Write-TestLog "Output: $statusText" "ERROR"
        return $false
    }
}

function Test-StopCommand {
    Write-TestLog "Test 3: stop command" "INFO"

    # Record PID before stopping
    $pidBeforeStop = $null
    if (Test-Path $PID_FILE) {
        $pidBeforeStop = Get-Content $PID_FILE
        Write-TestLog "PID before stop: $pidBeforeStop" "INFO"
    }

    # Execute stop command
    Write-TestLog "Executing stop command..." "INFO"
    & $START_SCRIPT stop | Out-Null

    # Wait for stop to complete
    Start-Sleep -Seconds 3

    # Check if PID file is deleted
    if (!(Test-Path $PID_FILE)) {
        Write-TestLog "PID file correctly deleted" "SUCCESS"

        # Check if process really stopped
        if ($pidBeforeStop) {
            $process = Get-Process -Id $pidBeforeStop -ErrorAction SilentlyContinue
            if (!$process) {
                Write-TestLog "Process correctly stopped (PID: $pidBeforeStop)" "SUCCESS"
                return $true
            } else {
                Write-TestLog "Process still running (PID: $pidBeforeStop)" "ERROR"
                return $false
            }
        } else {
            Write-TestLog "Cannot verify process stop (unknown PID), but PID file deleted" "SUCCESS"
            return $true
        }
    } else {
        Write-TestLog "PID file not deleted" "ERROR"
        return $false
    }
}

function Test-ProcessCleanup {
    Write-TestLog "Test 4: process cleanup" "INFO"

    # Start server for cleanup test
    Write-TestLog "Starting server for cleanup test..." "INFO"
    $job = Start-Job -ScriptBlock {
        param($scriptPath)
        & $scriptPath start -Background
    } -ArgumentList $START_SCRIPT

    # Wait for startup
    Start-Sleep -Seconds 5

    # Check PID file
    if (Test-Path $PID_FILE) {
        $serverPid = Get-Content $PID_FILE
        Write-TestLog "Server started (PID: $serverPid)" "INFO"

        # Simulate script interruption
        Write-TestLog "Simulating script interruption..." "INFO"
        Stop-Job $job -Force
        Remove-Job $job -Force

        # Manual cleanup (simulate signal handler)
        Write-TestLog "Manual process cleanup..." "INFO"
        $process = Get-Process -Id $serverPid -ErrorAction SilentlyContinue
        if ($process) {
            Stop-Process -Id $serverPid -Force -ErrorAction SilentlyContinue
            Write-TestLog "Process cleaned up" "SUCCESS"
        }

        # Clean PID file
        if (Test-Path $PID_FILE) {
            Remove-Item $PID_FILE -Force
            Write-TestLog "PID file cleaned up" "SUCCESS"
        }

        return $true
    } else {
        Write-TestLog "Server startup failed, cannot test cleanup" "ERROR"
        Stop-Job $job -Force
        Remove-Job $job -Force
        return $false
    }
}

function Main {
    if ($Help) {
        Show-Help
        exit 0
    }

    Write-TestLog "Starting process management tests" "INFO"
    Write-TestLog "Script path: $START_SCRIPT" "INFO"
    Write-TestLog "Log directory: $LOG_DIR" "INFO"
    Write-TestLog "PID file: $PID_FILE" "INFO"
    Write-Host ""

    # Check if start script exists
    if (!(Test-Path $START_SCRIPT)) {
        Write-TestLog "Start script not found: $START_SCRIPT" "ERROR"
        exit 1
    }

    $testResults = @()

    # Execute tests
    try {
        $testResults += Test-PidFileGeneration
        $testResults += Test-StatusCommand
        $testResults += Test-StopCommand
        $testResults += Test-ProcessCleanup
    } catch {
        Write-TestLog "Error during testing: $($_.Exception.Message)" "ERROR"
        # Ensure cleanup
        & $START_SCRIPT stop | Out-Null
        exit 1
    }

    # Final cleanup
    Write-TestLog "Final cleanup..." "INFO"
    & $START_SCRIPT stop | Out-Null

    # Count results
    $passedTests = ($testResults | Where-Object { $_ -eq $true }).Count
    $totalTests = $testResults.Count

    Write-Host ""
    Write-TestLog "Testing completed" "INFO"
    Write-TestLog "Passed tests: $passedTests/$totalTests" "INFO"

    if ($passedTests -eq $totalTests) {
        Write-TestLog "All tests passed! Process management working correctly" "SUCCESS"
        exit 0
    } else {
        Write-TestLog "Some tests failed, please check process management" "ERROR"
        exit 1
    }
}

# Execute main function
Main
