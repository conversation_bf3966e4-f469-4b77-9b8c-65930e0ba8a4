# File Transfer SDK

一个基于Spring Boot的、生产级的高性能文件传输SDK，提供分块上传、断点续传、秒传、多用户管理、权限控制、动态限速、数据库容错、智能清理机制等一系列强大功能。

## 核心特性

- **高性能传输**: 支持分块上传与多线程分块下载，充分利用网络带宽。
- **断点续传**: 上传或下载中断后可从断点无缝续传，支持基于HTTP Range的下载。
- **智能秒传**: 基于文件MD5进行秒传判断，通过物理文件校验确保文件一致性，避免误传。支持跨用户秒传和智能保留策略。备注：秒传成功时直接从init接口获取完整文件信息（包括relativePath），无需调用后续接口。
- **多用户与权限**: 支持多用户隔离，可为不同用户配置独立的存储路径、密钥和权限。
- **动态速率限制**: 可为每个用户独立设置上传和下载速率限制，并可在服务运行时动态调整。
- **数据库容错**: 即使数据库服务异常，系统仍能基于文件系统的元数据信息提供下载服务，保证核心可用性。
- **智能清理机制**: 自动清理过期记录，永久保护所有成功传输记录以最大化秒传功能，智能清理失败记录，确保系统性能和存储效率。
- **丰富的管理功能**: 提供API进行数据库健康检查、备份、以及从物理文件反向重建数据库。
- **多版本构建支持**: 支持Java 8、11、17、21的多版本构建，使用Java 8语言特性但可编译为不同字节码版本，满足不同部署环境需求。JAR文件自动添加版本后缀（如：`xxx-java8.jar`, `xxx-java21.jar`）。
- **无状态认证**: 基于 `HMAC-SHA256` 的无状态认证机制，安全且易于集成。

## 1. 服务端配置 (`application.yml`)

通过 `file.transfer.server` 前缀进行配置。系统支持全局服务配置和针对特定用户的个性化配置。

### 完整配置示例

以下示例展示了完整的服务端配置，包括全局服务设置和用户个性化配置：

```yaml
# Spring Boot 基础配置
server:
  port: 49011
  servlet:
    # 设置context-path，所有API将以此为前缀
    context-path: /filetransfer

# 文件传输服务端配置
file:
  transfer:
    server:
      # =======================================
      # 全局服务配置
      # =======================================
      enabled: true                    # 是否启用文件传输服务

      # 数据库配置
      database-path: ./data/file-transfer/database.db

      # 智能清理配置
      cleanup-enabled: true            # 是否启用自动清理功能
      cleanup-interval: 3600000        # 清理间隔时间（毫秒），默认1小时
      record-expire-time: 86400000     # 传输记录过期时间（毫秒），默认24小时
      chunk-expire-time: 604800000     # 分块记录过期时间（毫秒），默认7天
      failed-record-retain-time: 259200000  # 失败记录保留时间（毫秒），默认3天
      max-batch-delete-size: 1000      # 批量删除的最大记录数，避免数据库性能问题

      # 服务功能配置
      swagger-enabled: true            # 是否启用Swagger文档
      cors-enabled: true               # 是否启用跨域支持
      allowed-origins:                 # 允许的跨域来源
        - "*"

      # =======================================
      # 用户配置 (按用户名定义)
      # =======================================
      users:
        # "admin" 用户拥有管理员权限
        admin:
          role: "admin"                 # 管理员角色，可访问管理接口
          secret-key: "admin-secret-key-2024"
          storage-path: ./data/admin/files
          upload-rate-limit: 52428800   # 上传限速: 50 MB/s (字节/秒)
          download-rate-limit: 52428800 # 下载限速: 50 MB/s
          max-file-size: **********     # 最大文件: 1 GB
          default-chunk-size: 4194304   # 分块大小: 4 MB
          max-in-memory-size: 52428800  # 最大内存: 50 MB
          fast-upload-enabled: true     # 启用秒传
          rate-limit-enabled: false     # 管理员不限速

        # "demo" 用户用于演示和测试
        demo:
          role: "user"                  # 普通用户角色
          secret-key: "demo-secret-key-2024"
          storage-path: ./data/demo/files
          upload-rate-limit: 10485760   # 上传限速: 10 MB/s
          download-rate-limit: 10485760 # 下载限速: 10 MB/s
          max-file-size: 104857600      # 最大文件: 100 MB
          default-chunk-size: 1048576   # 分块大小: 1 MB
          max-in-memory-size: 10485760  # 最大内存: 10 MB
          fast-upload-enabled: true     # 启用秒传
          rate-limit-enabled: true      # 启用速度限制
```

### 配置逻辑说明

#### 默认配置机制
系统在代码中预设了默认配置值，当用户配置中某个属性未指定时，会自动使用以下默认值：

```java
// 系统默认配置（硬编码）
storage-path: "./data/file-transfer/files"
upload-rate-limit: 10485760L          // 10MB/s
download-rate-limit: 10485760L        // 10MB/s
default-chunk-size: 2097152L          // 2MB
max-file-size: 104857600L             // 100MB
max-in-memory-size: 10485760L         // 10MB
fast-upload-enabled: true
rate-limit-enabled: true
role: USER                            // 普通用户角色
```

#### 配置继承和覆盖
- 当请求某个用户的配置时，系统首先查找该用户的特定配置
- 如果用户不存在，则返回系统默认配置
- 如果用户存在但某些属性未配置，则用默认值填充这些属性
- 用户的 `secret-key` 和 `role` 必须明确配置，不能使用默认值

#### 重要说明
- **用户必须在配置文件中明确定义才能使用系统**
- **每个用户必须配置独立的 `secret-key`**
- **只有 `role: "admin"` 的用户才能访问管理接口**

#### 清理配置最佳实践

**生产环境推荐配置：**
```yaml
# 平衡性能和功能的生产环境配置
cleanup-enabled: true
cleanup-interval: 3600000              # 1小时清理一次
record-expire-time: 172800000          # 48小时
chunk-expire-time: 86400000            # 24小时（传输完成后及时清理）
failed-record-retain-time: 259200000   # 3天（保留用于问题分析）
max-batch-delete-size: 500             # 适中的批量大小
```

**开发测试环境配置：**
```yaml
# 快速清理的开发环境配置
cleanup-enabled: true
cleanup-interval: 300000               # 5分钟清理一次
record-expire-time: 3600000            # 1小时
chunk-expire-time: 7200000             # 2小时
failed-record-retain-time: 3600000     # 1小时
max-batch-delete-size: 100             # 较小的批量大小
```

## 2. 客户端配置

客户端通过编程方式进行配置，使用统一的 `ClientConfig` 类和 `ClientConfigBuilder` 构建器。

### 基础配置示例

```java
import com.sdesrd.filetransfer.client.config.ClientConfig;
import com.sdesrd.filetransfer.client.config.ClientConfigBuilder;
import com.sdesrd.filetransfer.client.FileTransferClient;

// 方式1：使用Builder进行详细配置（推荐）
ClientConfig clientConfig = ClientConfigBuilder.create()
    .serverAddr("127.0.0.1")
    .serverPort(49011)
    .contextPath("filetransfer") // 与服务端 context-path 匹配
    .auth("admin", "admin-secret-key-2024")
    .chunkSize(2 * 1024 * 1024L) // 2MB 分块
    .timeouts(120) // 统一设置超时时间为120秒
    .maxConcurrentTransfers(3) // 并发传输数
    .retry(5, 1000) // 5次重试，间隔1秒
    .build();

// 方式2：本地开发环境快速配置
ClientConfig localConfig = ClientConfigBuilder.localConfig(
    "demo", "demo-secret-key-2024");

// 方式3：使用便捷方法快速配置
ClientConfig quickConfig = ClientConfigBuilder.quickConnect(
    "127.0.0.1", "admin", "admin-secret-key-2024");

// 方式4：使用预设的生产环境配置
ClientConfig prodConfig = ClientConfigBuilder.productionConfig(
    "prod.example.com", 443, "admin", "admin-secret-key-2024");

// 创建客户端实例
FileTransferClient client = new FileTransferClient(clientConfig);

// 后续可使用 client.uploadFileWithId(...) 或 client.downloadFile(...)
```

### 高级配置示例

```java
// 高性能配置 - 适用于大文件传输场景
ClientConfig highPerfConfig = ClientConfigBuilder.create()
    .serverAddr("fast.example.com")
    .serverPort(443)
    .useHttps(true) // 启用HTTPS
    .auth("user", "secret")
    .chunkSize(4 * 1024 * 1024L) // 4MB大分块
    .maxConcurrentTransfers(8) // 高并发
    .connectTimeout(60) // 连接超时60秒
    .readTimeout(180) // 读取超时180秒
    .writeTimeout(300) // 写入超时300秒
    .maxIdleConnections(10) // 最大空闲连接数
    .keepAliveDuration(10) // 连接保活时间（分钟）
    .retry(3, 500) // 快速重试
    .build();

// 或使用预设的高性能配置
ClientConfig fastConfig = ClientConfigBuilder.highPerformanceConfig(
    "fast.example.com", "user", "secret");
```

### 配置参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `serverAddr` | localhost | 服务器地址 |
| `serverPort` | 49011 | 服务器端口 |
| `contextPath` | filetransfer | 服务端上下文路径 |
| `useHttps` | false | 是否使用HTTPS |
| `chunkSize` | 2MB | 分块大小（字节） |
| `connectTimeoutSeconds` | 30 | 连接超时时间（秒） |
| `readTimeoutSeconds` | 60 | 读取超时时间（秒） |
| `writeTimeoutSeconds` | 60 | 写入超时时间（秒） |
| `maxConcurrentTransfers` | 3 | 最大并发传输数 |
| `maxIdleConnections` | 5 | 最大空闲连接数 |
| `keepAliveDurationMinutes` | 5 | 连接保活时间（分钟） |
| `retryCount` | 3 | 重试次数 |
| `retryIntervalMs` | 1000 | 重试间隔（毫秒） |

## 3. API 接口

所有API都以服务端配置的 `server.servlet.context-path` 为前缀，默认为 `/filetransfer`。

### 3.1 认证机制

#### 认证要求
大部分接口都需要认证，但以下接口**无需认证**：
- `GET /api/file/health` - 基础健康检查
- Swagger文档相关接口 (`/swagger-*`, `/doc.html`, `/v2/api-docs`)
- Spring Boot Actuator监控接口 (`/actuator/*`)
- 静态资源接口 (`/static/*`, `/css/*`, `/js/*`, `/images/*`)

#### 认证头格式
需要认证的接口必须在HTTP请求头中包含以下两个字段：

- `X-File-Transfer-User`: 用户名 (例如: `admin`)
- `X-File-Transfer-Auth`: 认证令牌

#### 认证令牌生成算法

**客户端实现步骤：**
1. 获取当前时间的毫秒级时间戳 `timestamp`
2. 构造待签名字符串 `data = username + ":" + timestamp`
3. 使用用户的 `secretKey` 对 `data` 进行 `HMAC-SHA256` 加密，并将结果进行 `Base64` 编码，得到 `signature`
4. 最终的认证令牌为 `token = username + ":" + timestamp + ":" + signature`

**服务端验证：**
- 解析令牌格式：`username:timestamp:signature`
- 验证用户名匹配
- 检查时间戳是否在5分钟有效期内（防止重放攻击）
- 使用相同算法计算签名并比对

#### 角色权限控制
- **普通用户** (`role: "user"`): 只能访问基础文件传输接口 (`/api/file/*`)
- **管理员** (`role: "admin"`): 可以访问所有接口，包括管理接口 (`/api/admin/*`) 和数据库管理接口 (`/api/database/*`)

#### 认证错误响应
- **401 Unauthorized**: 缺少认证信息或认证失败
  ```json
  {"code":401,"message":"缺少认证信息，请提供用户名和认证令牌","data":null}
  ```
- **403 Forbidden**: 权限不足（普通用户访问管理接口）
  ```json
  {"code":403,"message":"权限不足，需要管理员角色才能访问此接口","data":null}
  ```

### 3.2 文件传输接口

基础路径: `/api/file`

#### **上传流程**

**1. `POST /upload/init` - 初始化上传**

- **描述**: 开始一个文件上传任务，服务端会检查秒传可能性，并返回一个唯一的 `transferId`。秒传成功时，此接口会直接返回完整的文件信息，客户端无需调用后续的chunk和complete接口。
- **请求体**: `application/json`
  ```json
  {
      "fileId": "01HN2Z8X9K7Q3M5P6R8S9T0V1W", // 客户端指定的ULID格式文件ID（必填）
      "originalFileName": "mydocument.zip", // 客户端原始文件名
      "fileExtension": "zip", // 文件后缀名 (可选, 若不提供会从originalFileName中提取)
      "fileSize": 10485760, // 文件总大小 (字节)
      "fileMd5": "d41d8cd98f00b204e9800998ecf8427e", // 完整文件的MD5
      "chunkSize": 2097152 // 分块大小 (字节, 可选)
  }
  ```

- **响应体结构**:
  ```json
  {
      "code": 200,
      "data": {
          "transferId": "a1b2c3d4-e5f6-...", // 本次传输的唯一ID
          "fileId": "01HN2Z8X9K7Q3M5P6R8S9T0V1W", // 文件的ULID标识
          "fileName": "d41d8cd98f00b204e9800998ecf8427e.zip", // 服务端存储名
          "chunkSize": 2097152,
          "totalChunks": 5,
          "fastUpload": false, // 是否已通过秒传完成

          // 以下字段在秒传时有值，普通上传时为null
          "relativePath": "202506/01HN2Z8X9K7Q3M5P6R8S9T0V1W/d41d8cd98f00b204e9800998ecf8427e.zip", // 文件相对路径
          "fileMd5": "d41d8cd98f00b204e9800998ecf8427e", // 文件MD5值
          "completeTime": "2024-05-20T10:00:00Z", // 上传完成时间
          "fileSize": 10485760, // 文件大小（字节）
          "transferDuration": 150 // 传输耗时（毫秒）
      }
  }
  ```

#### **🔥 秒传与普通上传流程对比**

| 场景 | fastUpload | 后续操作 | 获取完整信息的方式 | 新增字段值 |
|------|------------|----------|-------------------|------------|
| **秒传成功** | `true` | ✅ **无需调用chunk和complete接口** | 直接从init响应获取 | 有值 |
| **普通上传** | `false` | ❌ 需要调用chunk上传数据，然后调用complete | 从complete响应获取 | null |

**秒传场景示例响应**：
```json
{
    "code": 200,
    "data": {
        "transferId": "fast-upload-12345",
        "fileId": "01HN2Z8X9K7Q3M5P6R8S9T0V1W",
        "fileName": "d41d8cd98f00b204e9800998ecf8427e.zip",
        "chunkSize": 2097152,
        "totalChunks": 5,
        "fastUpload": true,
        // 🎯 秒传时直接返回完整信息
        "relativePath": "202506/01HN2Z8X9K7Q3M5P6R8S9T0V1W/d41d8cd98f00b204e9800998ecf8427e.zip",
        "fileMd5": "d41d8cd98f00b204e9800998ecf8427e",
        "completeTime": "2024-05-20T10:00:00Z",
        "fileSize": 10485760,
        "transferDuration": 150
    }
}
```

**普通上传场景示例响应**：
```json
{
    "code": 200,
    "data": {
        "transferId": "normal-upload-67890",
        "fileId": "01HN2Z8X9K7Q3M5P6R8S9T0V1W",
        "fileName": "d41d8cd98f00b204e9800998ecf8427e.zip",
        "chunkSize": 2097152,
        "totalChunks": 5,
        "fastUpload": false,
        // 🔄 普通上传时这些字段为null，需要从complete接口获取
        "relativePath": null,
        "fileMd5": null,
        "completeTime": null,
        "fileSize": null,
        "transferDuration": null
    }
}
```

**2. `POST /upload/chunk` - 上传文件分块**

- **描述**: 上传一个文件分块，请求体为 `multipart/form-data`。**注意**：当init接口返回`fastUpload: true`时，无需调用此接口。
- **适用场景**: 仅用于普通上传（`fastUpload: false`）
- **表单参数**:
    - `transferId`: 初始化时获取的传输ID。
    - `chunkIndex`: 分块的序号 (从0开始)。
    - `chunkMd5`: 当前分块数据的MD5。
    - `chunk`: 文件分块的二进制数据。
- **响应体**:
  ```json
  {
      "code": 200,
      "message": "分块上传成功"
  }
  ```

**3. `POST /upload/complete/{transferId}` - 完成文件上传**

- **描述**: 所有分块上传完毕后，通知服务端进行文件合并与最终校验。**注意**：当init接口返回`fastUpload: true`时，无需调用此接口。
- **适用场景**: 仅用于普通上传（`fastUpload: false`）
- **路径参数**: `transferId`
- **响应体**:
  ```json
  {
      "code": 200,
      "message": "文件上传完成",
      "data": {
          "transferId": "a1b2c3d4-e5f6-...",
          "fileId": "01HN2Z8X9K7Q3M5P6R8S9T0V1W",
          "fileName": "d41d8cd98f00b204e9800998ecf8427e.zip",
          "relativePath": "202506/01HN2Z8X9K7Q3M5P6R8S9T0V1W/d41d8cd98f00b204e9800998ecf8427e.zip",
          "fileSize": 10485760,
          "fileMd5": "d41d8cd98f00b204e9800998ecf8427e",
          "completeTime": "2024-05-20T10:00:00Z",
          "transferDuration": 5000 // 传输耗时(毫秒)
      }
  }
  ```

#### **下载流程**

**1. `GET /download/info/{fileId}` - 获取文件信息**

- **描述**: 在下载前获取文件的元数据。
- **路径参数**: `fileId`
- **响应体**:
  ```json
  {
      "code": 200,
      "data": {
          "fileId": "01HN2Z8X9K7Q3M5P6R8S9T0V1W",
          "fileName": "mydocument.zip",
          "fileSize": 10485760,
          "uploadTime": "2024-05-20T10:00:00Z"
      }
  }
  ```

**2. `GET /download/chunk/{fileId}` - 分块下载文件**

- **描述**: 下载文件的指定部分，支持 `Range` 请求头，是实现多线程下载和断点续传的基础。
- **路径参数**: `fileId`
- **请求头**: `Range: bytes=0-2097151` (请求文件的前2MB)
- **成功响应**:
    - **状态码**: `206 Partial Content`
    - **响应头**: `Content-Range: bytes 0-2097151/10485760`
    - **响应体**: 文件的二进制数据流。

**3. `GET /download/{fileId}` - 完整文件下载**

- **描述**: 下载完整文件。
- **路径参数**: `fileId`
- **成功响应**:
    - **状态码**: `200 OK`
    - **响应体**: 完整文件的二进制数据流。

#### **其他接口**

**`GET /progress/{transferId}` - 查询上传进度**
- **描述**: 查询指定传输任务的进度信息
- **认证**: 需要认证
- **响应体**:
  ```json
  {
      "code": 200,
      "data": {
          "transferId": "a1b2c3d4-e5f6-...",
          "totalChunks": 5,
          "completedChunks": 3,
          "progress": 60.0,
          "status": "uploading"
      }
  }
  ```

**`GET /health` - 基础健康检查**
- **描述**: 简单的服务健康检查，返回服务状态
- **认证**: 无需认证
- **响应体**:
  ```json
  {
      "code": 200,
      "message": "服务运行正常",
      "data": {
          "status": "UP",
          "timestamp": "2024-05-20T10:00:00Z"
      }
  }
  ```

### 3.3 系统管理接口

基础路径: `/api/admin` (**需要管理员权限**)

**`GET /statistics` - 获取传输统计信息**
- **描述**: 获取实时的传输统计信息，包括活跃连接、总流量等
- **认证**: 需要管理员权限
- **响应体**:
  ```json
  {
      "code": 200,
      "data": {
          "activeConnections": 5,
          "totalUploaded": **********,
          "totalDownloaded": **********,
          "activeTransfers": 3,
          "completedTransfers": 127
      }
  }
  ```

**`GET /health` - 获取系统健康状况**
- **描述**: 获取详细的系统健康状况，包括JVM内存使用情况
- **认证**: 需要管理员权限
- **响应体**:
  ```json
  {
      "code": 200,
      "data": {
          "status": "UP",
          "jvm": {
              "usedMemory": 134217728,
              "maxMemory": **********,
              "freeMemory": 939524096
          },
          "disk": {
              "totalSpace": **********00,
              "freeSpace": 53687091200
          }
      }
  }
  ```

**`GET /clear-rate-limiters` - 清理限流器缓存**
- **描述**: 清空所有用户的速率限制器缓存，用于动态更新配置后立即生效
- **认证**: 需要管理员权限
- **响应体**:
  ```json
  {
      "code": 200,
      "message": "限流器缓存清理完成",
      "data": null
  }
  ```

**`GET /cleanup/statistics` - 获取清理统计信息**
- **描述**: 获取系统清理操作的详细统计信息，包括清理次数、清理的记录数量等
- **认证**: 需要管理员权限
- **响应体**:
  ```json
  {
      "code": 200,
      "data": {
          "totalCleanedTransferRecords": 1250,
          "totalCleanedChunkRecords": 3750,
          "totalCleanupOperations": 48,
          "totalCleanupFailures": 2,
          "successRate": 95.83,
          "lastCleanupOperation": {
              "operationId": "1719396000000",
              "startTime": "2024-06-26T14:00:00Z",
              "endTime": "2024-06-26T14:00:05Z",
              "status": "COMPLETED",
              "cleanedTransferRecords": 25,
              "cleanedChunkRecords": 75,
              "durationMs": 5000
          }
      }
  }
  ```

**`POST /cleanup/manual` - 手动触发清理操作**
- **描述**: 立即执行一次清理操作，清理过期的传输记录和分块记录
- **认证**: 需要管理员权限
- **响应体**:
  ```json
  {
      "code": 200,
      "message": "清理操作已触发",
      "data": {
          "operationId": "1719396120000",
          "startTime": "2024-06-26T14:02:00Z",
          "status": "RUNNING"
      }
  }
  ```

**`GET /cleanup/config` - 获取清理配置信息**
- **描述**: 获取当前的清理配置参数
- **认证**: 需要管理员权限
- **响应体**:
  ```json
  {
      "code": 200,
      "data": {
          "cleanupEnabled": true,
          "cleanupIntervalMs": 3600000,
          "recordExpireTimeMs": 86400000,
          "chunkExpireTimeMs": 604800000,
          "failedRecordRetainTimeMs": 259200000,

          "maxBatchDeleteSize": 1000
      }
  }
  ```

### 3.4 数据库管理接口

基础路径: `/api/database` (**需要管理员权限**)

**`GET /health` - 检查数据库健康状态**
- **描述**: 检查数据库的详细健康状态，包括连接性和性能指标
- **认证**: 需要管理员权限
- **响应体**:
  ```json
  {
      "code": 200,
      "data": {
          "healthy": true,
          "databasePath": "./data/file-transfer/database.db",
          "connectionTest": "SUCCESS",
          "lastCheckTime": "2024-05-20T10:00:00Z",
          "recordCount": 1250
      }
  }
  ```

**`POST /backup` - 创建数据库备份**
- **描述**: 创建当前数据库的备份文件
- **认证**: 需要管理员权限
- **响应体**:
  ```json
  {
      "code": 200,
      "message": "数据库备份创建成功",
      "data": "database-20240520_100000.db"
  }
  ```

**`GET /backups` - 列出数据库备份**
- **描述**: 列出所有可用的数据库备份文件
- **认证**: 需要管理员权限
- **响应体**:
  ```json
  {
      "code": 200,
      "data": [
          {
              "fileName": "database-20240520_100000.db",
              "fileSize": 1048576,
              "createTime": "2024-05-20T10:00:00Z"
          }
      ]
  }
  ```

**`GET /backup/download/{fileName}` - 下载备份文件**
- **描述**: 下载指定的备份文件
- **认证**: 需要管理员权限
- **响应**: 文件二进制流

**`POST /rebuild` - 重建数据库**
- **描述**: 从磁盘上的 `info.json` 元数据文件反向扫描，重建整个文件数据库
- **认证**: 需要管理员权限
- **响应体**:
  ```json
  {
      "code": 200,
      "message": "数据库重建成功",
      "data": {
          "success": true,
          "scannedFiles": 1250,
          "rebuiltRecords": 1250,
          "duration": 5000
      }
  }
  ```

## 4. 核心功能逻辑详解

### 数据库容错 (回落) 机制

这是保障服务高可用的核心设计，确保即使数据库故障也能继续提供文件下载服务。

#### **容错机制组成**

1. **元数据冗余存储**
   - 每个文件成功上传后，除了在SQLite数据库中记录信息，还会在其存储目录下创建 `info.json` 文件
   - `info.json` 包含完整的文件元数据：原始文件名、大小、MD5、上传时间等
   - 存储路径示例：`./data/demo/files/202405/01HN2Z8X9K7Q3M5P6R8S9T0V1W/info.json`

2. **数据库健康检查**
   - 检查超时时间：5秒
   - 检查间隔：30秒
   - 检查内容：数据库连接性、查询响应时间

3. **自动回落触发条件**
   - 数据库健康检查失败
   - 数据库连接超时
   - 在数据库中未找到文件记录

4. **回落模式工作流程**
   ```
   请求文件 -> 检查数据库健康 -> 数据库不健康或记录不存在
                                    ↓
   根据fileId计算存储路径 -> 查找info.json -> 读取元数据 -> 提供下载服务
   ```

5. **灾难恢复机制**
   - 使用 `POST /api/database/rebuild` 接口
   - 扫描所有用户存储目录
   - 读取所有 `info.json` 文件
   - 重建完整的SQLite数据库
   - 最大扫描文件数限制：1,000,000个（防止内存溢出）

#### **技术参数**
```java
DATABASE_HEALTH_CHECK_TIMEOUT_MS = 5000L;    // 健康检查超时
DATABASE_HEALTH_CHECK_INTERVAL_MS = 30000L;  // 健康检查间隔
MAX_SCAN_FILES_LIMIT = 1000000;                // 最大扫描文件数
```

### 用户鉴权与限速

- **鉴权**: 如 "认证机制" 部分所述，通过 `X-File-Transfer-User` 和 `X-File-Transfer-Auth` 请求头进行HMAC签名验证。
- **限速**:
    - **实现**: 基于 Google Guava 的 `RateLimiter` 实现。
    - **粒度**: 为每个用户的 `upload` 和 `download` 操作独立创建限速器。
    - **应用**: 在上传和下载的数据流处理循环中，通过 `acquire()` 方法精确控制数据通过速率，从而匹配配置的字节/秒限制。
    - **动态性**: 限速配置在用户请求时动态加载，并且可以通过 `/api/admin/clear-rate-limiters` 接口清空缓存，使配置变更即时生效。

### 智能秒传机制详解

系统提供了两种级别的秒传检测机制，既能最大化存储效率，又保证数据安全隔离。配合智能清理策略，确保秒传功能的长期有效性。

#### **秒传触发条件**

**1. fileId + MD5 完全匹配秒传**
- **触发条件**: 客户端指定的 `fileId` 在系统中已存在，且现有文件的 MD5 值与当前上传文件完全匹配
- **验证过程**: 系统会重新计算现有物理文件的 MD5 确保文件完整性
- **适用场景**: 客户端重复上传完全相同的文件到相同的 `fileId`
- **处理方式**: 直接返回成功，无需重新上传
- **清理保护**: 即使数据库记录被清理，也能通过 `info.json` 元数据文件实现秒传

**2. 仅 MD5 匹配秒传（跨用户共享）**
- **触发条件**: 客户端指定的 `fileId` 不存在，但系统中存在其他 `fileId` 的文件，其 MD5 值与当前上传文件匹配
- **跨用户检测**: **系统会检索所有用户的文件记录**，只要任一用户曾上传过相同 MD5 的文件就能触发秒传
- **适用场景**: 不同用户上传相同内容的文件，如共同使用的软件包、文档模板等
- **处理方式**: 将已存在的文件复制到当前用户的专属存储路径
- **智能保护**: 清理系统采用智能保留策略，优先保护可能用于秒传的记录

#### **数据隔离与安全机制**

虽然秒传检测是跨用户的，但系统保证了严格的数据隔离：

- **用户独立存储**: 每个用户都有独立的存储路径配置（如 `./data/admin/files` vs `./data/demo/files`）
- **物理文件复制**: 即使触发秒传，系统也会将已存在的文件**完整复制**到当前用户的专属存储路径
- **独立生命周期**: 每个 `fileId` 都有独立的存储目录和元数据文件，互不影响

#### **秒传工作流程示例**

```
场景：用户B上传与用户A相同内容的文件

1. 用户A上传文件（MD5: abc123）→ 存储到 /data/admin/files/202312/fileId1/abc123.pdf
2. 用户B上传相同文件（MD5: abc123）→ 系统检测到MD5匹配
3. 秒传触发 → 复制文件到 /data/demo/files/202312/fileId2/abc123.pdf
4. 自动生成info.json → 在 /data/demo/files/202312/fileId2/ 目录下创建完整元数据文件
5. 返回完整信息 → init接口直接返回relativePath、fileMd5、completeTime等完整信息
6. 结果：两个用户都有独立的文件副本，物理隔离，互不影响，客户端获得完整文件信息
```

### 文件标识符（`fileId`）的唯一性与生命周期

系统设计确保了每个文件在服务端的标识符 (`fileId`) 都是唯一的，从根本上杜绝了因 `fileId` 重复导致的数据混乱或覆盖问题。

- **客户端指定**: `fileId` **由客户端在 `POST /upload/init` 接口调用时提供**。客户端必须提供符合ULID标准的26字符文件标识符。
- **唯一性保障**: 系统采用 **ULID (Universally Unique Lexicographically Sortable Identifier)** 算法作为 `fileId` 格式。ULID 结合了高精度的时间戳和随机数，能有效保证在高并发场景下的唯一性。
- **智能冲突检测**: 服务端会验证客户端提供的 `fileId` 格式有效性。当 `fileId` 已存在时，会进一步检查文件MD5：如果MD5匹配则触发秒传，如果MD5不匹配则拒绝请求并抛出文件完整性异常。
- **独立的存储路径**: **每个 `fileId` 都拥有独立的存储路径**。即使是秒传，系统也会将已存在的文件复制到新 `fileId` 对应的存储目录下，确保每个文件标识符都有独立、隔离的物理存储位置和完整的生命周期管理。

这个机制使得每个文件都拥有一个从创建到删除的完整、隔离的生命周期，确保了系统的健壮性和数据一致性。

### 智能清理机制详解

系统采用多层次的智能清理策略，在保持数据库性能的同时，确保核心功能（秒传、下载、断点续传）的完整性。

#### **清理策略层次**

**1. 传输记录清理**
- **成功记录保护**: **所有已完成（COMPLETED）状态的传输记录永远不会被自动清理**，确保秒传功能的最大可用性
- **状态过滤清理**: 只清理失败（FAILED）、取消（CANCELLED）、暂停（PAUSED）、卡住的传输中（TRANSFERRING）等非成功状态的过期记录
- **智能保留策略**: 对非成功记录，数量少时（≤50条）暂不清理，数量多时保留25%较新记录用于问题分析
- **容错保障**: 只清理有 `info.json` 元数据文件的记录，确保容错机制可用
- **秒传功能最大化**: 通过保护所有成功记录，确保跨用户秒传和同用户秒传的长期有效性

**2. 分块记录清理**
- **即时清理**: 传输完成后立即清理对应的分块记录，释放存储空间
- **孤立清理**: 自动清理对应传输记录已被删除的孤立分块记录
- **失败清理**: 按配置时间清理过期的失败分块记录
- **断点保护**: 保留进行中传输的分块记录，不影响断点续传功能

**3. 失败记录清理**
- **数据库清理**: 清理过期的失败传输记录，释放数据库空间
- **并发安全**: 使用锁机制防止同一记录的并发清理操作
- **保留物理文件**: 只清理数据库记录，保留物理文件用于故障排查

**4. 冲突避免机制**
- **重建检测**: 清理前检查数据库重建状态，避免资源竞争
- **状态协调**: 清理操作与数据库重建操作互斥执行
- **优雅降级**: 检测到冲突时跳过清理，确保系统稳定性

#### **清理配置参数**

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `cleanup-enabled` | true | 是否启用自动清理功能 |
| `cleanup-interval` | 3600000ms (1小时) | 清理任务执行间隔 |
| `record-expire-time` | 86400000ms (24小时) | 传输记录过期时间 |
| `chunk-expire-time` | 604800000ms (7天) | 分块记录过期时间 |
| `failed-record-retain-time` | 259200000ms (3天) | 失败记录保留时间 |

| `max-batch-delete-size` | 1000 | 批量删除最大记录数 |

#### **清理对核心功能的影响**

- **秒传功能**: ✅ **所有成功传输记录永久保护**，确保秒传功能的最大可用性和长期有效性，同用户秒传通过 `info.json` 完全不受影响
- **下载功能**: ✅ 三层容错机制（数据库→路径规则→info.json）确保下载服务不中断
- **断点续传**: ✅ 状态感知清理策略保护进行中的传输，不影响断点续传
- **数据一致性**: ✅ 清理操作与重建操作协调执行，确保数据一致性
- **系统性能**: ✅ 通过清理失败和异常状态记录，保持数据库性能，同时保留所有有价值的成功记录

## 5. 代码文件目录结构说明

```
file-transfer-sdk/
├── file-transfer-server-sdk/           # 服务端SDK核心模块
│   ├── src/main/java/com/sdesrd/filetransfer/server/
│   │   ├── controller/                 # REST API控制器层
│   │   │   ├── FileTransferController.java      # 文件传输核心接口
│   │   │   ├── FileTransferAdminController.java # 管理接口（状态查询等）
│   │   │   └── DatabaseManagementController.java # 数据库管理接口
│   │   ├── service/                    # 业务逻辑服务层
│   │   │   ├── FileTransferService.java         # 文件传输核心服务
│   │   │   ├── AuthService.java                # 认证服务（API签名验证）
│   │   │   ├── DatabaseFallbackService.java    # 数据库容错服务
│   │   │   ├── FileTransferMonitorService.java # 传输监控和智能清理服务
│   │   │   ├── CleanupStatisticsService.java   # 清理统计服务
│   │   │   └── DatabaseManagementService.java  # 数据库管理服务
│   │   ├── interceptor/                # 拦截器
│   │   │   └── AuthInterceptor.java            # 认证拦截器（角色权限控制）
│   │   ├── config/                     # 配置类
│   │   │   ├── FileTransferProperties.java     # 主配置类（含清理配置）
│   │   │   ├── CleanupConstants.java           # 清理配置常量类
│   │   │   ├── UserConfig.java                # 用户配置（含角色管理）
│   │   │   └── FileTransferAutoConfiguration.java # 自动配置
│   │   ├── entity/                     # 数据实体
│   │   │   ├── FileTransferRecord.java         # 文件传输记录实体
│   │   │   ├── FileChunkRecord.java            # 文件分块记录实体
│   │   │   └── FileDownloadLog.java            # 文件下载日志实体
│   │   ├── mapper/                     # 数据访问层
│   │   │   ├── FileTransferRecordMapper.java   # 传输记录映射器
│   │   │   ├── FileChunkRecordMapper.java      # 分块记录映射器
│   │   │   └── FileDownloadLogMapper.java      # 下载日志映射器
│   │   ├── dto/                        # 数据传输对象
│   │   │   ├── FileUploadInitRequest.java      # 上传初始化请求
│   │   │   ├── FileUploadInitResponse.java     # 上传初始化响应
│   │   │   ├── FileUploadCompleteResponse.java # 上传完成响应
│   │   │   ├── TransferProgressResponse.java   # 传输进度响应
│   │   │   ├── FileInfo.java                   # 文件信息
│   │   │   ├── ApiResult.java                  # 统一API响应格式
│   │   │   └── SystemHealthResponse.java       # 系统健康响应
│   │   ├── exception/                  # 异常类
│   │   │   ├── FileTransferException.java      # 基础异常类
│   │   │   └── FileIntegrityException.java     # 文件完整性异常
│   │   └── util/                       # 工具类
│   │       ├── UlidUtils.java                  # ULID工具类
│   │       ├── FileUtils.java                  # 文件操作工具
│   │       └── RateLimitUtils.java             # 速率限制工具
│   └── src/test/java/                  # 单元测试
├── file-transfer-client-sdk/           # 客户端SDK模块
│   ├── src/main/java/com/sdesrd/filetransfer/client/
│   │   ├── FileTransferClient.java             # 客户端主类
│   │   ├── config/                     # 客户端配置
│   │   │   ├── ClientConfig.java               # 统一配置类
│   │   │   └── ClientConfigBuilder.java       # 配置构建器
│   │   ├── dto/                        # 数据传输对象
│   │   │   ├── ApiResult.java                  # API响应结果
│   │   │   ├── FileInfo.java                   # 文件信息
│   │   │   ├── FileUploadInitRequest.java      # 上传初始化请求
│   │   │   ├── FileUploadInitResponse.java     # 上传初始化响应
│   │   │   ├── FileUploadCompleteResponse.java # 上传完成响应
│   │   │   ├── TransferProgress.java           # 传输进度
│   │   │   ├── UploadResult.java               # 上传结果
│   │   │   └── DownloadResult.java             # 下载结果
│   │   ├── listener/                   # 监听器接口
│   │   │   └── TransferListener.java           # 传输监听器
│   │   ├── exception/                  # 异常类
│   │   │   └── FileTransferException.java      # 客户端异常
│   │   └── util/                       # 客户端工具类
│   │       ├── AuthUtils.java                  # 认证工具类
│   │       ├── DownloadManager.java            # 下载管理器
│   │       ├── ConcurrentTransferManager.java  # 并发传输管理器
│   │       └── FileUtils.java                  # 文件工具类
│   └── src/test/java/                  # 客户端测试
├── file-transfer-client-demo/          # 客户端演示应用
│   └── src/main/java/
│       └── FileTransferClientDemo.java        # 演示程序主类
├── file-transfer-server-standalone/    # 独立服务端应用
│   ├── src/main/java/
│   │   └── FileTransferServerApplication.java # 独立服务端启动类
│   ├── src/main/resources/
│   │   └── file-transfer-server.yml           # 独立服务端配置
│   ├── start-server.sh                # Linux/Mac服务启动脚本（智能多版本JAR查找）
│   └── start-server.ps1              # Windows服务启动脚本（智能多版本JAR查找）
├── scripts/                           # 构建和测试脚本
│   ├── build-and-test.sh             # Linux/Mac多版本构建测试脚本（支持--java-version参数）
│   ├── build-and-test.ps1            # Windows多版本构建测试脚本（支持-JavaVersion参数）
│   ├── java-environment-check.sh     # Java环境检查脚本
│   ├── set-java-env.sh               # Linux/Mac Java环境设置脚本
│   └── set-java-env-en.bat           # Windows Java环境设置脚本
└── pom.xml                           # Maven父项目配置

**多版本构建产物:**
构建完成后，各模块的target目录下会生成对应Java版本的JAR文件：
```
file-transfer-server-sdk/target/
├── file-transfer-server-sdk-1.0.0-java8.jar
├── file-transfer-server-sdk-1.0.0-java8-sources.jar
└── file-transfer-server-sdk-1.0.0-java8-javadoc.jar

file-transfer-server-standalone/target/
├── file-transfer-server-standalone-1.0.0-java8.jar
├── file-transfer-server-standalone-1.0.0-java8-boot-java8.jar    # Spring Boot可执行JAR
├── file-transfer-server-standalone-1.0.0-java8-dist.tar.gz       # Linux分发包
├── file-transfer-server-standalone-1.0.0-java8-dist.zip          # Windows分发包
└── file-transfer-server-standalone-1.0.0-java8/                  # 完整分发目录
    ├── file-transfer-server-standalone-1.0.0-java8.jar
    ├── lib/                    # 74个依赖JAR文件
    ├── bin/                    # 启动脚本
    ├── config/                 # 配置文件
    └── ...                     # 其他目录
```

## 8. 构建和部署

### 8.1 多版本构建系统

项目支持针对不同Java版本的构建，使用Java 8语言特性但可编译为不同字节码版本，确保在各种Java运行环境下的兼容性。

#### 支持的Java版本
- **Java 8**: 最大兼容性，适合传统环境
- **Java 11**: LTS版本，推荐用于生产环境  
- **Java 17**: LTS版本，现代Java特性支持
- **Java 21**: 最新LTS版本，最佳性能

#### 构建命令

**Linux/macOS:**
```bash
# 构建Java 8版本（默认）
./build-and-test.sh build

# 构建指定Java版本
./build-and-test.sh build --java-version java8
./build-and-test.sh build --java-version java11
./build-and-test.sh build --java-version java17
./build-and-test.sh build --java-version java21

# 构建并测试
./build-and-test.sh build-test --java-version java21
```

**Windows:**
```powershell
# 构建Java 8版本（默认）
.\build-and-test.ps1 -Mode build

# 构建指定Java版本
.\build-and-test.ps1 -Mode build -JavaVersion java8
.\build-and-test.ps1 -Mode build -JavaVersion java11
.\build-and-test.ps1 -Mode build -JavaVersion java17
.\build-and-test.ps1 -Mode build -JavaVersion java21

# 构建并测试
.\build-and-test.ps1 -Mode build-test -JavaVersion java21
```

### 8.2 构建产物

#### JAR文件命名规范
构建系统会自动为不同Java版本的JAR文件添加版本后缀：

```
file-transfer-server-sdk-1.0.0-java8.jar
file-transfer-server-sdk-1.0.0-java11.jar
file-transfer-server-sdk-1.0.0-java17.jar
file-transfer-server-sdk-1.0.0-java21.jar

file-transfer-client-sdk-1.0.0-java8.jar
file-transfer-client-sdk-1.0.0-java11.jar
file-transfer-client-sdk-1.0.0-java17.jar
file-transfer-client-sdk-1.0.0-java21.jar
```

#### 独立服务器分发包
独立服务器模块会生成完整的分发包，包含所有依赖和启动脚本：

```
file-transfer-server-standalone-1.0.0-java8/
├── file-transfer-server-standalone-1.0.0-java8.jar
├── lib/                    # 依赖JAR文件（74个）
├── config/                 # 配置文件
├── bin/                    # 启动脚本
│   ├── start-server.sh     # Linux/Mac启动脚本
│   └── start-server.ps1    # Windows启动脚本
├── data/                   # 数据目录
├── logs/                   # 日志目录
├── temp/                   # 临时目录
├── docs/                   # 文档目录
└── README.txt              # 部署说明
```

分发包同时提供压缩包格式：
- `file-transfer-server-standalone-1.0.0-java8-dist.tar.gz`（Linux/Mac）
- `file-transfer-server-standalone-1.0.0-java8-dist.zip`（Windows）

### 8.3 部署配置

#### 版本选择建议
选择的JAR版本应匹配或低于目标运行环境的Java版本：

| 构建版本 | 运行环境要求 | 适用场景 |
|----------|-------------|----------|
| java8    | Java 8+     | 传统环境，最大兼容性 |
| java11   | Java 11+    | 生产环境推荐 |
| java17   | Java 17+    | 现代化部署 |
| java21   | Java 21+    | 最新环境，最佳性能 |

#### 独立服务器部署

**快速部署（开发环境）:**
```bash
# 1. 构建项目
./build-and-test.sh build --java-version java8

# 2. 启动服务器
cd file-transfer-server-standalone
./start-server.sh start --background
```

**生产环境部署:**
```bash
# 1. 解压分发包
tar -xzf file-transfer-server-standalone-1.0.0-java11-dist.tar.gz
cd file-transfer-server-standalone-1.0.0-java11

# 2. 修改配置
vim config/file-transfer-server.yml

# 3. 启动服务
./bin/start-server.sh start --background

# 4. 验证服务
./bin/start-server.sh status
```

**Windows部署:**
```powershell
# 1. 解压分发包
Expand-Archive file-transfer-server-standalone-1.0.0-java11-dist.zip
cd file-transfer-server-standalone-1.0.0-java11

# 2. 启动服务
.\bin\start-server.ps1 start -Background

# 3. 查看状态
.\bin\start-server.ps1 status
```

### 8.4 技术特点

#### 向下兼容设计
- **语言特性**: 使用Java 8语言特性和API，确保代码兼容性
- **字节码版本**: 通过Maven `--release`参数控制字节码版本
- **API一致性**: 所有版本保持相同的API和功能特性

#### Java 21兼容性
针对Java 21的模块系统限制，系统自动添加必要的JVM参数：
```bash
--add-opens java.base/java.lang=ALL-UNNAMED
--add-opens java.base/java.lang.reflect=ALL-UNNAMED
--add-opens java.base/java.util=ALL-UNNAMED
--add-opens java.base/java.io=ALL-UNNAMED
# ... 更多模块开放配置
```

#### 构建系统优化
- **Maven Profiles**: 使用profile机制实现多版本构建
- **并行构建**: 支持`-T 1C`并行编译，提高构建速度
- **依赖管理**: 统一的依赖版本管理，确保各版本一致性
- **测试兼容**: 完整的测试覆盖，包括反射API兼容性测试

### 8.5 持续集成建议

**基础CI配置:**
```yaml
# GitHub Actions示例
strategy:
  matrix:
    java-version: [java8, java11, java17, java21]
    
steps:
  - name: Build and Test
    run: ./build-and-test.sh build-test --java-version ${{ matrix.java-version }}
```

**生产环境推荐:**
- 至少构建Java 8和Java 17两个版本
- 在CI/CD流水线中并行构建多个版本
- 为不同环境准备对应版本的部署包

## 9. 数据库容错机制详解

系统设计了完善的数据库容错机制，即使在数据库服务完全不可用的情况下，仍能基于文件系统的元数据信息提供核心的文件下载服务。容错机制与清理系统协调工作，确保系统的高可用性。

### **容错机制工作原理**

**1. 元数据文件 (info.json)**
每个上传的文件都会在其存储目录下生成一个 `info.json` 元数据文件，包含：
- 文件基本信息（ID、名称、大小、MD5等）
- 传输状态和时间戳
- 用户信息和权限数据
- 文件路径和存储位置

**2. 三层查找机制**
当数据库不可用时，系统采用以下查找策略：
- **第一层**: 尝试从数据库查询文件记录
- **第二层**: 基于文件路径规则直接查找物理文件
- **第三层**: 读取 `info.json` 元数据文件构建虚拟记录

**3. 自动重建机制**
- **健康检查**: 系统定期检查数据库健康状态
- **自动扫描**: 检测到数据库恢复后，自动扫描文件系统重建记录
- **增量同步**: 只重建缺失的记录，避免重复操作
- **清理协调**: 重建操作与清理操作互斥执行，避免资源竞争

**4. 清理系统的容错保障**
- **元数据检查**: 清理前验证 `info.json` 文件存在，确保容错机制可用
- **物理文件保护**: 只清理数据库记录，永远保留物理文件和元数据
- **智能恢复**: 清理后的记录可通过容错机制自动重建

## 10. 服务端磁盘文件存储目录结构说明

### 存储架构设计

文件传输SDK采用**全局共享数据库 + 用户独立文件存储**的架构设计：

#### 全局数据库存储
```
${database-path}/                       # 全局数据库文件位置（由database-path配置）
└── file-transfer.db                   # 所有用户共享的SQLite数据库文件
```

#### 用户文件存储（每个用户独立）
```
${user-storage-path}/                   # 用户专用存储目录（由用户配置的storage-path决定）
├── 202312/                            # 年月分区目录（YYYYMM格式）
│   ├── 01HN2Z8X9K7Q3M5P6R8S9T0V1W/    # ULID格式的fileId目录
│   │   ├── d41d8cd98f00b204e9800998ecf8427e.txt    # MD5.扩展名格式的实际文件
│   │   └── info.json                  # 文件元数据
│   ├── 01HN2Z9Y0L8R4N6Q7S9U1V2W3X/    # 另一个文件的ULID目录
│   │   └── a1b2c3d4e5f6789012345678901234567890.pdf
│   └── ...
├── 202401/                            # 下一个月的分区
├── ...
```

### 临时分块文件存储
上传分块文件会直接存储在对应的 fileId 目录下,每个分片文件的命名格式为：
```
${user-storage-path}/YYYYMM/{fileId}/
├── {md5}.{ext}                   # 合并完成后的最终文件
├── info.json                     # 文件元数据
├── {md5}.{ext}.chunk.000000      # 第一个分片（临时）
├── {md5}.{ext}.chunk.000001      # 第二个分片（临时）
└── ...                           # 其他分片
```
合并完成后，所有 `.chunk.*` 临时分片文件会被删除。

#### 完整系统目录结构示例
```
/data/                                 # 系统根目录
├── file-transfer/                     # 全局数据库目录
│   ├── database.db                    # 共享SQLite数据库（所有用户的传输记录）
│   └── backup/                        # 数据库备份目录
│       ├── database-20241201_120000.db
│       └── database-20241201_180000.db
├── admin/                             # 管理员用户的文件存储
│   ├── 202312/
│   │   └── 01HN2Z8X9K7Q3M5P6R8S9T0V1W/
│   │       └── d41d8cd98f00b204e9800998ecf8427e.txt
│   └── temp/
└── demo/                              # 演示用户demo的文件存储
    ├── 202312/
    │   └── 01HN2Z9Y0L8R4N6Q7S9U1V2W3X/
    │       └── a1b2c3d4e5f6789012345678901234567890.pdf
    └── temp/