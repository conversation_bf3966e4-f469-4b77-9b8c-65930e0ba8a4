# ================================================================================
# 文件传输独立服务端启动脚本（PowerShell版本）
# ================================================================================

param(
    [Parameter(Position=0)]
    [ValidateSet("start", "stop", "restart", "status", "logs")]
    [string]$Command,
    
    [int]$Port = 49011,
    [string]$Profile = "server",
    [string]$Config = "application.yml",
    [string]$JavaHome = "",
    [switch]$Background,
    [switch]$Help
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# ==================== 常量定义 ====================

# 脚本版本信息
$SCRIPT_VERSION = "1.0.0"
$SCRIPT_NAME = "文件传输独立服务端启动脚本"

# 服务配置
$DEFAULT_SERVER_PORT = 49011
$DEFAULT_PROFILE = "server"
$DEFAULT_CONFIG_FILE = "application.yml"

# JAR文件配置 - 支持多版本JAR文件名
$JAR_BASE_NAME = "file-transfer-server-standalone-1.0.0"
$TARGET_DIR = "target"

# 日志配置
$LOG_DIR = "logs"
$SERVER_PID_FILE = "$LOG_DIR\server.pid"
$LOG_FILE = "$LOG_DIR\server.log"

# 超时配置
$STARTUP_TIMEOUT = 45   # 服务器启动超时时间（秒）- 减少以避免在测试中卡住
$SHUTDOWN_TIMEOUT = 15  # 服务器关闭超时时间（秒）

# ==================== 日志函数 ====================

function Write-InfoLog {
    param([string]$Message)
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    Write-Host "[INFO] $timestamp - $Message" -ForegroundColor Blue
}

function Write-SuccessLog {
    param([string]$Message)
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    Write-Host "[SUCCESS] $timestamp - $Message" -ForegroundColor Green
}

function Write-WarningLog {
    param([string]$Message)
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    Write-Host "[WARNING] $timestamp - $Message" -ForegroundColor Yellow
}

function Write-ErrorLog {
    param([string]$Message)
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    Write-Host "[ERROR] $timestamp - $Message" -ForegroundColor Red
}

# ==================== 工具函数 ====================

function Show-Header {
    Write-Host "========================================================" -ForegroundColor White
    Write-Host "    $SCRIPT_NAME" -ForegroundColor White
    Write-Host "    版本：$SCRIPT_VERSION" -ForegroundColor White
    Write-Host "    时间：$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor White
    Write-Host "========================================================" -ForegroundColor White
}

function Show-Help {
    Write-Host "用法: .\start-server.ps1 [命令] [选项]"
    Write-Host ""
    Write-Host "命令:"
    Write-Host "  start     启动服务器"
    Write-Host "  stop      停止服务器"
    Write-Host "  restart   重启服务器"
    Write-Host "  status    查看服务器状态"
    Write-Host "  logs      查看服务器日志"
    Write-Host ""
    Write-Host "选项:"
    Write-Host "  -Port PORT           指定服务器端口 (默认: $DEFAULT_SERVER_PORT)"
    Write-Host "  -Profile PROFILE     指定Spring配置文件 (默认: $DEFAULT_PROFILE)"
    Write-Host "  -Config CONFIG       指定配置文件路径 (默认: $DEFAULT_CONFIG_FILE)"
    Write-Host "  -JavaHome PATH       指定Java JDK路径 (可选，默认使用系统Java)"
    Write-Host "  -Background          后台运行服务器"
    Write-Host "  -Help                显示此帮助信息"
    Write-Host ""
    Write-Host "示例:"
    Write-Host "  .\start-server.ps1 start                                        # 启动服务器"
    Write-Host "  .\start-server.ps1 start -Port 8080                             # 在端口8080启动服务器"
    Write-Host "  .\start-server.ps1 start -Background                            # 后台启动服务器"
    Write-Host "  .\start-server.ps1 start -JavaHome 'C:\Java\jdk21'              # 使用指定的Java启动服务器"
    Write-Host "  .\start-server.ps1 start -JavaHome 'C:\Program Files\Java\jdk' # 支持包含空格的路径"
    Write-Host "  .\start-server.ps1 stop                                         # 停止服务器"
    Write-Host "  .\start-server.ps1 status                                       # 查看服务器状态"
    Write-Host ""
}

function Initialize-Logging {
    if (!(Test-Path $LOG_DIR)) {
        New-Item -ItemType Directory -Path $LOG_DIR -Force | Out-Null
        Write-InfoLog "创建日志目录：$LOG_DIR"
    }
}

function Setup-JavaEnvironment {
    param([string]$CustomJavaHome)
    
    # 如果指定了自定义Java路径，则使用它
    if ($CustomJavaHome) {
        if ((Test-Path $CustomJavaHome) -and (Test-Path "$CustomJavaHome\bin\java.exe")) {
            $env:JAVA_HOME = $CustomJavaHome
            # 安全处理包含空格的路径：使用引号包围路径
            $javaBinPath = "`"$CustomJavaHome\bin`""
            if ($env:PATH -notlike "*$javaBinPath*") {
                $env:PATH = "$javaBinPath;$env:PATH"
            }
            Write-InfoLog "使用指定的Java JDK：$CustomJavaHome"
        } else {
            Write-ErrorLog "指定的Java JDK路径无效：$CustomJavaHome"
            return $false
        }
    } else {
        # 使用系统JAVA_HOME环境变量
        if ($env:JAVA_HOME -and (Test-Path "$env:JAVA_HOME\bin\java.exe")) {
            Write-InfoLog "使用系统JAVA_HOME环境变量：$env:JAVA_HOME"
        } else {
            Write-InfoLog "使用系统默认Java环境"
        }
    }
    
    try {
        $null = Get-Command java -ErrorAction Stop
    } catch {
        Write-ErrorLog "Java运行时未安装或未在PATH中"
        return $false
    }
    
    try {
        $javaVersionOutput = & java -version 2>&1
        $javaVersion = $javaVersionOutput[0] -replace '^.*\"(.*)\".*$', '$1'
        Write-InfoLog "当前Java版本：$javaVersion"
    } catch {
        Write-InfoLog "Java命令可用，但无法解析版本信息"
    }
    
    return $true
}

function Get-JarPath {
    # 定义可能的JAR文件名（包含多版本后缀）
    $jarNames = @(
        "$JAR_BASE_NAME-java8.jar",
        "$JAR_BASE_NAME-java11.jar",
        "$JAR_BASE_NAME-java17.jar",
        "$JAR_BASE_NAME-java21.jar",
        "$JAR_BASE_NAME.jar"  # 无后缀版本（向后兼容）
    )
    
    # 智能查找JAR文件：支持部署环境和开发环境
    $jarPaths = @()
    foreach ($jarName in $jarNames) {
        $jarPaths += @(
            # 1. 部署环境：JAR文件在bin目录的上级目录
            (Join-Path ".." $jarName),
            # 2. 开发环境：JAR文件在target分发目录（优先，因为有正确的lib依赖）
            (Join-Path "$TARGET_DIR\$($jarName -replace '\.jar$', '')" $jarName),
            # 3. 开发环境：JAR文件在target目录（但可能缺少依赖路径）
            (Join-Path $TARGET_DIR $jarName),
            # 4. 当前目录（如果直接在根目录运行，或者从bin目录查找上级目录）
            $jarName
        )
    }
    
    foreach ($jarPath in $jarPaths) {
        if (Test-Path $jarPath) {
            Write-InfoLog "找到JAR文件：$jarPath"
            
            # 检查对应的lib目录是否存在（用于验证依赖完整性）
            $jarDir = Split-Path $jarPath -Parent
            # 处理相对路径为空的情况（如当前目录的JAR文件）
            if ([string]::IsNullOrEmpty($jarDir)) {
                $jarDir = "."
            }
            $libDir = Join-Path $jarDir "lib"
            
            if (Test-Path $libDir) {
                $libCount = (Get-ChildItem $libDir -Filter "*.jar").Count
                Write-InfoLog "检测到依赖库目录：$libDir ($libCount 个JAR文件)"
                return $jarPath
            } else {
                Write-WarningLog "JAR文件存在但缺少依赖库目录：$libDir"
                # 继续检查其他路径
            }
        }
    }
    
    Write-ErrorLog "未找到有效的JAR文件（包含依赖库）"
    Write-InfoLog "已搜索以下JAR文件名："
    foreach ($jarName in $jarNames) {
        Write-InfoLog "  - $jarName"
    }
    Write-InfoLog ""
    Write-InfoLog "已搜索以下路径模式："
    Write-InfoLog "  - ..\[JAR文件名] （部署环境）"
    Write-InfoLog "  - target\[JAR目录]\[JAR文件名] （开发环境分发目录）"
    Write-InfoLog "  - target\[JAR文件名] （开发环境target目录）"
    Write-InfoLog "  - [JAR文件名] （当前目录）"
    Write-InfoLog ""
    Write-InfoLog "请确认："
    Write-InfoLog "  1. 如果是部署环境，请确保JAR文件在上级目录"
    Write-InfoLog "  2. 如果是开发环境，请先运行构建命令："
    Write-InfoLog "     cd .. && .\build-and-test.ps1 build -JavaVersion java8"
    Write-InfoLog "  3. 确保lib目录与JAR文件在同一目录下"
    Write-InfoLog "  4. 检查构建输出目录结构是否正确"
    
    return $null
}

function Test-Port {
    param([int]$PortNumber)
    
    try {
        $connection = Test-NetConnection -ComputerName localhost -Port $PortNumber -InformationLevel Quiet -WarningAction SilentlyContinue
        if ($connection) {
            Write-WarningLog "端口 $PortNumber 已被占用"
            return $false
        }
    } catch {
        # 端口未被占用或无法连接
    }
    
    return $true
}

function Get-ServerPid {
    if (Test-Path $SERVER_PID_FILE) {
        try {
            $serverPid = Get-Content $SERVER_PID_FILE
            $process = Get-Process -Id $serverPid -ErrorAction SilentlyContinue
            if ($process) {
                return $serverPid
            } else {
                # PID文件存在但进程不存在，删除PID文件
                Remove-Item $SERVER_PID_FILE -Force
            }
        } catch {
            # 忽略错误
        }
    }
    
    return $null
}

function Start-Server {
    param([int]$ServerPort, [string]$ServerProfile, [string]$ConfigFile, [bool]$RunInBackground)
    
    Write-InfoLog "启动文件传输服务器..."
    
    # 检查服务器是否已经运行
    $existingPid = Get-ServerPid
    if ($existingPid) {
        Write-WarningLog "服务器已经在运行 (PID: $existingPid)"
        return $false
    }
    
    # 检查端口
    if (!(Test-Port $ServerPort)) {
        Write-ErrorLog "无法启动服务器，端口 $ServerPort 已被占用"
        return $false
    }
    
    # 检查JAR文件
    $jarPath = Get-JarPath
    if (!$jarPath) {
        return $false
    }
    $javaOpts = "-Xms512m", "-Xmx1g", "-XX:+UseG1GC"
    $springOpts = "--server.port=$ServerPort", "--spring.profiles.active=$ServerProfile"
    
    # 智能处理配置文件路径
    if ($ConfigFile -and ($ConfigFile -ne $DEFAULT_CONFIG_FILE)) {
        $springOpts += "--spring.config.location=classpath:/$ConfigFile"
    } else {
        # 为部署环境添加配置文件路径
        $configPaths = @(
            "../config/",           # 部署环境：config目录在上级目录
            "config/",              # 开发环境：config目录在当前目录  
            "src/main/resources/"   # 开发环境：Maven资源目录
        )
        
        $validConfigPaths = @()
        foreach ($configPath in $configPaths) {
            if (Test-Path $configPath) {
                $validConfigPaths += "file:$configPath"
            }
        }
        
        if ($validConfigPaths.Count -gt 0) {
            $configLocation = $validConfigPaths -join ","
            $springOpts += "--spring.config.location=$configLocation"
            Write-InfoLog "配置文件搜索路径：$configLocation"
        }
    }
    
    $javaArgs = $javaOpts + @("-jar", $jarPath) + $springOpts
    
    Write-InfoLog "Java命令：java $($javaArgs -join ' ')"
    
    if ($RunInBackground) {
        # 后台启动
        try {
            # 为stderr使用不同的文件以避免PowerShell重定向冲突
            $errorLogFile = "$LOG_DIR\server-error.log"
            
            $process = Start-Process -FilePath "java" -ArgumentList $javaArgs -PassThru -RedirectStandardOutput $LOG_FILE -RedirectStandardError $errorLogFile -WindowStyle Hidden
            $serverPid = $process.Id
            Set-Content -Path $SERVER_PID_FILE -Value $serverPid
            
            Write-InfoLog "服务器后台启动中 (PID: $serverPid)..."
            Write-InfoLog "等待服务器启动，最多等待 $STARTUP_TIMEOUT 秒..."
            
            # 等待服务器启动
            $waitCount = 0
            while ($waitCount -lt $STARTUP_TIMEOUT) {
                # 每10秒输出一次进度
                if ($waitCount % 10 -eq 0 -and $waitCount -gt 0) {
                    Write-InfoLog "等待服务器启动中... ($waitCount/$STARTUP_TIMEOUT 秒)"
                }
                try {
                    $response = Invoke-WebRequest -Uri "http://localhost:$ServerPort/filetransfer/actuator/health" -TimeoutSec 1 -ErrorAction SilentlyContinue
                    if ($response.StatusCode -eq 200) {
                        Write-SuccessLog "服务器启动成功！"
                        Write-InfoLog "服务器地址: http://localhost:$ServerPort"
                        Write-InfoLog "API文档: http://localhost:$ServerPort/filetransfer/doc.html"
                        Write-InfoLog "健康检查: http://localhost:$ServerPort/filetransfer/actuator/health"
                        Write-InfoLog "日志文件: $LOG_FILE"
                        Write-InfoLog "PID文件: $SERVER_PID_FILE"
                        return $true
                    }
                } catch {
                    # 忽略连接错误，继续等待
                }
                
                Start-Sleep -Seconds 1
                $waitCount++
                
                # 检查进程是否还在运行
                if (!(Get-Process -Id $serverPid -ErrorAction SilentlyContinue)) {
                    Write-ErrorLog "服务器进程意外退出"
                    
                    # 读取并显示错误日志
                    if (Test-Path $errorLogFile) {
                        Write-ErrorLog "服务器错误日志内容："
                        Get-Content $errorLogFile -ErrorAction SilentlyContinue | ForEach-Object { Write-Host "  $_" -ForegroundColor Red }
                    }
                    
                    Remove-Item $SERVER_PID_FILE -Force -ErrorAction SilentlyContinue
                    return $false
                }
            }
            
            Write-ErrorLog "服务器启动超时"
            
            # 读取并显示错误日志
            if (Test-Path $errorLogFile) {
                Write-ErrorLog "服务器错误日志内容："
                Get-Content $errorLogFile -ErrorAction SilentlyContinue | ForEach-Object { Write-Host "  $_" -ForegroundColor Red }
            }
            
            Stop-Server
            return $false
        } catch {
            Write-ErrorLog "启动服务器时发生错误：$($_.Exception.Message)"
            
            # 读取并显示错误日志
            $errorLogFile = "$LOG_DIR\server-error.log"
            if (Test-Path $errorLogFile) {
                Write-ErrorLog "服务器错误日志内容："
                Get-Content $errorLogFile -ErrorAction SilentlyContinue | ForEach-Object { Write-Host "  $_" -ForegroundColor Red }
            }
            
            return $false
        }
    } else {
        # 前台启动
        Write-InfoLog "服务器前台启动中..."
        Write-InfoLog "按 Ctrl+C 停止服务器"
        
        try {
            $process = Start-Process -FilePath "java" -ArgumentList $javaArgs -PassThru -Wait
            return $true
        } catch {
            Write-ErrorLog "启动服务器时发生错误：$($_.Exception.Message)"
            return $false
        }
    }
}

function Stop-Server {
    Write-InfoLog "停止文件传输服务器..."
    
    $serverPid = Get-ServerPid
    if (!$serverPid) {
        Write-WarningLog "服务器未运行"
        return $true
    }
    
    Write-InfoLog "停止服务器进程 (PID: $serverPid)..."
    
    try {
        # 发送停止信号
        $process = Get-Process -Id $serverPid -ErrorAction SilentlyContinue
        if ($process) {
            Stop-Process -Id $serverPid -ErrorAction SilentlyContinue
            
            # 等待进程退出
            $waitCount = 0
            while ($waitCount -lt $SHUTDOWN_TIMEOUT) {
                $proc = Get-Process -Id $serverPid -ErrorAction SilentlyContinue
                if (!$proc) {
                    Remove-Item $SERVER_PID_FILE -Force -ErrorAction SilentlyContinue
                    Write-SuccessLog "服务器已停止"
                    return $true
                }
                
                Start-Sleep -Seconds 1
                $waitCount++
            }
            
            # 强制杀死进程
            Write-WarningLog "强制停止服务器进程..."
            Stop-Process -Id $serverPid -Force -ErrorAction SilentlyContinue
            Remove-Item $SERVER_PID_FILE -Force -ErrorAction SilentlyContinue
            
            Write-SuccessLog "服务器已强制停止"
            return $true
        } else {
            Remove-Item $SERVER_PID_FILE -Force -ErrorAction SilentlyContinue
            Write-SuccessLog "服务器已停止"
            return $true
        }
    } catch {
        Write-ErrorLog "停止服务器时发生错误：$($_.Exception.Message)"
        Remove-Item $SERVER_PID_FILE -Force -ErrorAction SilentlyContinue
        return $false
    }
}

function Show-Status {
    $serverPid = Get-ServerPid
    if ($serverPid) {
        Write-SuccessLog "服务器正在运行 (PID: $serverPid)"
        
        # 尝试获取服务器信息
        try {
            $process = Get-Process -Id $serverPid
            $commandLine = (Get-WmiObject Win32_Process -Filter "ProcessId = $serverPid").CommandLine
            
            # 从命令行中提取端口
            if ($commandLine -match "--server\.port=(\d+)") {
                $serverPort = $matches[1]
            } else {
                $serverPort = $DEFAULT_SERVER_PORT
            }
            
            Write-Host "服务器信息:"
            Write-Host "  PID: $serverPid"
            Write-Host "  端口: $serverPort"
            Write-Host "  服务器地址: http://localhost:$serverPort"
            Write-Host "  API文档: http://localhost:$serverPort/filetransfer/doc.html"
            Write-Host "  健康检查: http://localhost:$serverPort/filetransfer/actuator/health"
            Write-Host "  日志文件: $LOG_FILE"
            Write-Host "  PID文件: $SERVER_PID_FILE"
            
            # 检查健康状态
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:$serverPort/filetransfer/actuator/health" -TimeoutSec 5 -ErrorAction SilentlyContinue
                if ($response.StatusCode -eq 200) {
                    Write-SuccessLog "服务器健康检查通过"
                } else {
                    Write-WarningLog "服务器健康检查失败"
                }
            } catch {
                Write-WarningLog "服务器健康检查失败"
            }
        } catch {
            Write-WarningLog "无法获取详细的服务器信息"
        }
    } else {
        Write-InfoLog "服务器未运行"
    }
}

function Show-Logs {
    if (Test-Path $LOG_FILE) {
        Write-InfoLog "显示服务器日志：$LOG_FILE"
        Write-Host "========================================"
        Get-Content $LOG_FILE -Wait
    } else {
        Write-WarningLog "日志文件不存在：$LOG_FILE"
    }
}

function Restart-Server {
    param([int]$ServerPort, [string]$ServerProfile, [string]$ConfigFile, [bool]$RunInBackground)
    
    Write-InfoLog "重启文件传输服务器..."
    
    # 停止服务器
    Stop-Server | Out-Null
    
    # 等待一秒
    Start-Sleep -Seconds 1
    
    # 启动服务器
    Start-Server $ServerPort $ServerProfile $ConfigFile $RunInBackground
}

# ==================== 主程序 ====================

function Main {
    # 显示帮助信息
    if ($Help) {
        Show-Help
        exit 0
    }
    
    # 如果没有指定命令，显示帮助
    if (!$Command) {
        Show-Help
        exit 1
    }
    
    # 显示脚本头部信息
    Show-Header
    
    # 初始化日志
    Initialize-Logging
    
    # 设置Java环境
    if (!(Setup-JavaEnvironment $JavaHome)) {
        exit 1
    }
    
    # 执行命令
    switch ($Command) {
        "start" {
            if (Start-Server $Port $Profile $Config $Background.IsPresent) {
                exit 0
            } else {
                exit 1
            }
        }
        "stop" {
            if (Stop-Server) {
                exit 0
            } else {
                exit 1
            }
        }
        "restart" {
            if (Restart-Server $Port $Profile $Config $Background.IsPresent) {
                exit 0
            } else {
                exit 1
            }
        }
        "status" {
            Show-Status
            exit 0
        }
        "logs" {
            Show-Logs
            exit 0
        }
        default {
            Write-ErrorLog "未知命令: $Command"
            exit 1
        }
    }
}

# 执行主函数
Main 